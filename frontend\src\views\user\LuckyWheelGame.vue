<template>
  <div class="lucky-wheel-game">
    <!-- 调试信息 -->
    <div style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; font-size: 12px; z-index: 9999;">
      <div>isValidated: {{ gameState.isValidated }}</div>
      <div>cdkCode: {{ gameState.cdkCode }}</div>
      <div>sessionId: {{ gameState.sessionId }}</div>
      <div>prizes.length: {{ prizes.length }}</div>
    </div>

    <!-- 页面头部 -->
    <div class="game-header">
      <h1 class="game-title">🎯 小里幸运转盘</h1>
      <p class="game-subtitle">输入激活码开始游戏，奖品等你来拿！</p>
    </div>

    <!-- CDK验证区域 -->
    <div v-if="!gameState.isValidated" class="cdk-section">
      <div class="cdk-input-container">
        <div class="input-group">
          <label for="cdkInput" class="input-label">请输入激活码：</label>
          <input
            id="cdkInput"
            v-model="cdkInput"
            type="text"
            placeholder="请输入您的激活码"
            class="cdk-input"
            :disabled="isValidating"
            @keyup.enter="validateCDK"
          />
          <button
            @click="validateCDK"
            :disabled="!cdkInput.trim() || isValidating"
            class="validate-btn"
          >
            {{ isValidating ? '验证中...' : '验证激活码' }}
          </button>
        </div>
        <div v-if="validationMessage" class="validation-message" :class="validationMessageType">
          {{ validationMessage }}
        </div>
      </div>
    </div>

    <!-- 游戏主界面 -->
    <div v-else class="game-main">
      <!-- 游戏信息栏 -->
      <div class="game-info">
        <div class="info-item">
          <span class="info-label">激活码：</span>
          <span class="info-value">{{ gameState.cdkCode }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">剩余次数：</span>
          <span class="info-value remaining-spins">{{ gameState.remainingSpins }}</span>
        </div>
      </div>

      <!-- 转盘区域 -->
      <div class="wheel-section">
        <div class="wheel-container">
          <!-- 指针 -->
          <div class="pointer">
            <div class="pointer-triangle"></div>
          </div>

          <!-- 转盘主体 -->
          <div
            class="wheel"
            :style="{ transform: `rotate(${currentRotation}deg)` }"
            :class="{ spinning: isSpinning }"
          >
            <!-- SVG转盘 -->
            <svg class="wheel-svg" viewBox="0 0 400 400">
              <!-- 转盘段落 -->
              <g v-for="(prize, index) in prizes" :key="prize.id">
                <!-- 扇形路径 -->
                <path
                  :d="getSegmentPath(index)"
                  :fill="getPrizeColor(index)"
                  class="wheel-segment-path"
                  stroke="rgba(255,255,255,0.3)"
                  stroke-width="2"
                />

                <!-- 奖品文本 - 竖直排列 -->
                <g v-if="prize.name">
                  <text
                    v-for="(char, charIndex) in prize.name"
                    :key="charIndex"
                    :x="getTextPosition(index).x"
                    :y="getTextPosition(index).y + (charIndex - (prize.name.length - 1) / 2) * getCharSpacing(prize.name)"
                    :transform="getTextTransform(index)"
                    class="segment-text-svg"
                    :style="{ fontSize: getTextSize(prize.name) }"
                    text-anchor="middle"
                    dominant-baseline="central"
                    fill="#ffffff"
                    stroke="#333333"
                    stroke-width="0.3"
                    font-weight="700"
                    font-family="Arial, Microsoft YaHei, sans-serif"
                  >
                    {{ char }}
                  </text>
                </g>
              </g>
            </svg>

            <!-- 中心按钮 -->
            <div
              class="wheel-center"
              @click="spin"
              :class="{ disabled: !canSpin }"
            >
              <div class="center-content">
                <div class="center-icon">{{ isSpinning ? '⏳' : '🎯' }}</div>
                <div class="center-text">{{ isSpinning ? '转动中' : 'SPIN' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 转盘控制 -->
        <div class="wheel-controls">
          <button
            @click="spin"
            :disabled="!canSpin"
            class="spin-btn"
          >
            {{ isSpinning ? '转动中...' : '开始转盘' }}
          </button>
        </div>
      </div>

      <!-- 历史记录 -->
      <div class="history-section">
        <h3 class="section-title">🏆 转盘记录</h3>
        <div v-if="spinHistory.length === 0" class="no-history">
          暂无转盘记录
        </div>
        <div v-else class="history-list">
          <div
            v-for="(record, index) in spinHistory"
            :key="index"
            class="history-item"
            :class="{ winning: record.isWinning }"
          >
            <div class="history-time">{{ formatTime(record.spinTime) }}</div>
            <div class="history-result">
              <span v-if="record.isWinning" class="prize-name">🎉 {{ record.prizeName }}</span>
              <span v-else class="no-prize">😔 未中奖</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 结果弹窗 -->
    <div v-if="showResultModal" class="result-modal-overlay" @click="closeResultModal">
      <div class="result-modal" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">🎊 转盘结果</h2>
          <button @click="closeResultModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div v-if="lastSpinResult.isWinning" class="winning-result">
            <div class="prize-icon">🏆</div>
            <h3 class="congratulations">恭喜中奖！</h3>
            <div class="prize-info">
              <div class="prize-name">{{ lastSpinResult.prizeName }}</div>
              <div class="prize-description">{{ lastSpinResult.prizeDescription }}</div>
            </div>
          </div>
          <div v-else class="losing-result">
            <div class="consolation-icon">😔</div>
            <h3 class="consolation">很遗憾，未中奖</h3>
            <p class="encouragement">不要灰心，继续努力！</p>
          </div>
        </div>
        <div class="modal-footer">
          <div class="remaining-info">
            剩余转盘次数：<span class="remaining-count">{{ gameState.remainingSpins }}</span>
          </div>
          <button @click="closeResultModal" class="continue-btn">
            {{ gameState.remainingSpins > 0 ? '继续游戏' : '游戏结束' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <div class="loading-text">{{ loadingText }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { luckyWheelApi } from '../../api/luckyWheelApi'

export default {
  name: 'LuckyWheelGame',
  setup() {
    // 响应式数据
    const cdkInput = ref('')
    const isValidating = ref(false)
    const validationMessage = ref('')
    const validationMessageType = ref('') // 'success' | 'error'
    
    const isLoading = ref(false)
    const loadingText = ref('')
    
    const isSpinning = ref(false)
    const currentRotation = ref(0)
    const showResultModal = ref(false)
    
    // 游戏状态
    const gameState = reactive({
      isValidated: false,
      cdkCode: '',
      sessionId: '',
      remainingSpins: 0,
      expireTime: null
    })
    
    // 奖品数据
    const prizes = ref([])
    const spinHistory = ref([])
    const lastSpinResult = reactive({
      isWinning: false,
      prizeName: '',
      prizeDescription: '',
      prizeIndex: null
    })
    
    // 计算属性
    const segmentAngle = computed(() => {
      return prizes.value.length > 0 ? 360 / prizes.value.length : 60
    })
    
    const canSpin = computed(() => {
      return gameState.isValidated && 
             gameState.remainingSpins > 0 && 
             !isSpinning.value && 
             !isLoading.value
    })
    
    // 方法
    const validateCDK = async () => {
      if (!cdkInput.value.trim()) {
        validationMessage.value = '请输入激活码'
        validationMessageType.value = 'error'
        return
      }
      
      isValidating.value = true
      validationMessage.value = ''
      
      try {
        const response = await luckyWheelApi.validateCDK(cdkInput.value.trim())
        
        console.log('完整的验证响应:', response)

        if (response.code === 200 && response.data && response.data.isValid) {
          console.log('CDK验证成功:', response.data)

          gameState.isValidated = true
          gameState.cdkCode = cdkInput.value.trim()
          gameState.sessionId = response.data.sessionId
          gameState.remainingSpins = response.data.remainingSpins
          gameState.expireTime = response.data.expireTime

          console.log('游戏状态已设置:', gameState)
          console.log('isValidated现在是:', gameState.isValidated)

          validationMessage.value = response.data.message
          validationMessageType.value = 'success'

          // 加载奖品和游戏状态
          await loadGameData()
        } else {
          console.error('CDK验证失败:', response)
          console.log('response.code:', response.code)
          console.log('response.data:', response.data)
          console.log('response.data.isValid:', response.data?.isValid)
          validationMessage.value = response.data?.message || response.message || '激活码验证失败'
          validationMessageType.value = 'error'
        }
      } catch (error) {
        console.error('CDK验证失败:', error)
        validationMessage.value = '验证失败，请检查网络连接'
        validationMessageType.value = 'error'
      } finally {
        isValidating.value = false
      }
    }
    
    const loadGameData = async () => {
      isLoading.value = true
      loadingText.value = '加载游戏数据...'

      try {
        console.log('开始加载游戏数据...')
        console.log('会话ID:', gameState.sessionId)
        console.log('CDK代码:', gameState.cdkCode)

        // 并行加载奖品和游戏状态
        const [prizesResponse, gameStateResponse] = await Promise.all([
          luckyWheelApi.getAvailablePrizes(),
          luckyWheelApi.getGameState(gameState.sessionId, gameState.cdkCode)
        ])

        console.log('奖品响应:', prizesResponse)
        console.log('游戏状态响应:', gameStateResponse)

        if (prizesResponse.code === 200 && prizesResponse.data) {
          prizes.value = prizesResponse.data
          console.log('奖品数据已设置:', prizes.value)
          console.log('奖品顺序:')
          prizes.value.forEach((prize, index) => {
            console.log(`索引${index}: ${prize.name}`)
          })
        } else {
          console.error('获取奖品失败:', prizesResponse)
        }

        if (gameStateResponse.code === 200 && gameStateResponse.data) {
          gameState.remainingSpins = gameStateResponse.data.remainingSpins
          spinHistory.value = gameStateResponse.data.spinHistory || []
          console.log('游戏状态已更新:', gameState)
        } else {
          console.error('获取游戏状态失败:', gameStateResponse)
        }
      } catch (error) {
        console.error('加载游戏数据失败:', error)
        validationMessage.value = '加载游戏数据失败'
        validationMessageType.value = 'error'
      } finally {
        isLoading.value = false
      }
    }

    const spin = async () => {
      if (!canSpin.value) return

      isSpinning.value = true
      isLoading.value = true
      loadingText.value = '转盘中...'

      try {
        const response = await luckyWheelApi.spin(gameState.sessionId, gameState.cdkCode)

        if (response.code === 200 && response.data) {
          const result = response.data

          // 更新游戏状态
          gameState.remainingSpins = result.remainingSpins

          // 计算转盘旋转角度
          // 指针固定在12点钟方向，需要让中奖奖品的中心旋转到12点钟位置
          let targetAngle = 0
          if (result.prizeIndex !== null && result.prizeIndex !== undefined) {
            // 每个扇形的角度
            const segmentAngle = 360 / prizes.value.length

            // 计算中奖奖品的中心角度位置
            // 转盘布局中索引0从-90度开始（12点钟位置）
            // 索引0: -90° 到 -30°，中心在 -60°
            // 索引1: -30° 到 30°，中心在 0°
            // 索引2: 30° 到 90°，中心在 60°
            const prizeStartAngle = result.prizeIndex * segmentAngle - 90
            const prizeCurrentAngle = prizeStartAngle + segmentAngle / 2

            // 要让奖品中心旋转到12点钟位置(-90度)，需要计算旋转角度
            // 由于我们要让奖品中心到达-90度位置，所以需要逆时针旋转
            targetAngle = (-90 - prizeCurrentAngle + 360) % 360

            console.log('中奖奖品索引:', result.prizeIndex)
            console.log('中奖奖品名称:', prizes.value[result.prizeIndex]?.name)
            console.log('扇形角度:', segmentAngle)
            console.log('奖品起始角度:', prizeStartAngle)
            console.log('奖品中心角度:', prizeCurrentAngle)
            console.log('目标旋转角度:', targetAngle)
          }

          // 添加多圈旋转效果（3-5圈）
          const extraRotations = 3 + Math.random() * 2
          const finalRotation = currentRotation.value + (extraRotations * 360) + targetAngle

          // 开始旋转动画
          currentRotation.value = finalRotation

          // 等待旋转动画完成
          setTimeout(() => {
            isSpinning.value = false
            isLoading.value = false

            // 设置结果数据
            lastSpinResult.isWinning = result.isWinning
            lastSpinResult.prizeName = result.prize?.name || ''
            lastSpinResult.prizeDescription = result.prize?.description || ''
            lastSpinResult.prizeIndex = result.prizeIndex

            // 显示结果弹窗
            showResultModal.value = true

            // 更新历史记录
            loadGameData()
          }, 3000) // 3秒转盘动画时间

        } else {
          isSpinning.value = false
          isLoading.value = false
          validationMessage.value = response.data?.message || response.message || '转盘失败'
          validationMessageType.value = 'error'
        }
      } catch (error) {
        console.error('转盘失败:', error)
        isSpinning.value = false
        isLoading.value = false
        validationMessage.value = '转盘失败，请检查网络连接'
        validationMessageType.value = 'error'
      }
    }

    const closeResultModal = () => {
      showResultModal.value = false
    }

    const getPrizeColor = (index) => {
      const colors = [
        '#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24',
        '#eb4d4b', '#a55eea', '#fd79a8', '#6c5ce7',
        '#26de81', '#f0932b', '#44a08d', '#96c93d',
        '#ee5a24', '#74b9ff', '#fdcb6e', '#e17055'
      ]
      return colors[index % colors.length]
    }

    // 根据文本长度和段落数量计算字体大小
    const getTextSize = (text) => {
      if (!text) return '16px'

      const length = text.length
      const segmentCount = prizes.value?.length || 8

      // 基础字体大小根据段落数量调整
      let baseSize = 20
      if (segmentCount > 16) baseSize = 14
      else if (segmentCount > 12) baseSize = 16
      else if (segmentCount > 8) baseSize = 18

      // 根据文本长度进一步调整
      if (length <= 2) return `${baseSize}px`
      if (length <= 3) return `${Math.max(baseSize - 1, 12)}px`
      if (length <= 4) return `${Math.max(baseSize - 2, 12)}px`
      if (length <= 5) return `${Math.max(baseSize - 3, 12)}px`
      return `${Math.max(baseSize - 4, 10)}px`
    }

    // 计算字符间距
    const getCharSpacing = (text) => {
      const fontSize = parseInt(getTextSize(text))
      return fontSize * 1.2 // 字符间距为字体大小的1.2倍
    }

    // 计算扇形路径
    const getSegmentPath = (index) => {
      const centerX = 200
      const centerY = 200
      const radius = 180
      const innerRadius = 60

      const segmentAngle = 360 / prizes.value.length
      const startAngle = index * segmentAngle - 90 // -90度让第一个扇形从顶部开始
      const endAngle = startAngle + segmentAngle

      const startAngleRad = (startAngle * Math.PI) / 180
      const endAngleRad = (endAngle * Math.PI) / 180

      // 外圆弧的起点和终点
      const x1 = centerX + radius * Math.cos(startAngleRad)
      const y1 = centerY + radius * Math.sin(startAngleRad)
      const x2 = centerX + radius * Math.cos(endAngleRad)
      const y2 = centerY + radius * Math.sin(endAngleRad)

      // 内圆弧的起点和终点
      const x3 = centerX + innerRadius * Math.cos(endAngleRad)
      const y3 = centerY + innerRadius * Math.sin(endAngleRad)
      const x4 = centerX + innerRadius * Math.cos(startAngleRad)
      const y4 = centerY + innerRadius * Math.sin(startAngleRad)

      const largeArcFlag = segmentAngle > 180 ? 1 : 0

      return `M ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} L ${x3} ${y3} A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4} Z`
    }

    // 计算文字位置
    const getTextPosition = (index) => {
      const centerX = 200
      const centerY = 200
      const textRadius = 110 // 文字距离中心的距离

      const segmentAngle = 360 / prizes.value.length
      const angle = index * segmentAngle + segmentAngle / 2 - 90 // 文字在扇形中央
      const angleRad = (angle * Math.PI) / 180

      return {
        x: centerX + textRadius * Math.cos(angleRad),
        y: centerY + textRadius * Math.sin(angleRad)
      }
    }

    // 计算文字旋转角度
    const getTextTransform = (index) => {
      const segmentAngle = 360 / prizes.value.length
      const angle = index * segmentAngle + segmentAngle / 2 - 90
      const textPos = getTextPosition(index)

      // 让文字从外边缘指向圆心（径向方向）
      // 为了让12点方向的文字是正的，需要在角度上加90度
      // 这样12点方向的文字就会是正常阅读方向
      let textAngle = angle + 90

      // 将角度标准化到0-360度范围
      textAngle = ((textAngle % 360) + 360) % 360

      return `rotate(${textAngle}, ${textPos.x}, ${textPos.y})`
    }

    const formatTime = (timeString) => {
      if (!timeString) return ''
      const date = new Date(timeString)
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 生命周期
    onMounted(() => {
      // 检查URL参数中是否有CDK
      const urlParams = new URLSearchParams(window.location.search)
      const urlCdk = urlParams.get('cdk')
      if (urlCdk) {
        cdkInput.value = urlCdk
        validateCDK()
      }
    })

    return {
      cdkInput,
      isValidating,
      validationMessage,
      validationMessageType,
      isLoading,
      loadingText,
      isSpinning,
      currentRotation,
      showResultModal,
      gameState,
      prizes,
      spinHistory,
      lastSpinResult,
      segmentAngle,
      canSpin,
      validateCDK,
      loadGameData,
      spin,
      closeResultModal,
      getPrizeColor,
      getTextSize,
      getCharSpacing,
      getSegmentPath,
      getTextPosition,
      getTextTransform,
      formatTime
    }
  }
}
</script>

<style scoped>
.lucky-wheel-game {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Arial', 'Microsoft YaHei', sans-serif;
}

/* 页面头部 */
.game-header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.game-title {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.game-subtitle {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

/* CDK验证区域 */
.cdk-section {
  max-width: 500px;
  margin: 0 auto;
  background: rgba(255,255,255,0.95);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.cdk-input-container {
  text-align: center;
}

.input-group {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
  color: #333;
  font-size: 1.1rem;
}

.cdk-input {
  width: 100%;
  max-width: 300px;
  padding: 12px 15px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  text-align: center;
  margin-bottom: 15px;
  transition: border-color 0.3s ease;
}

.cdk-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
}

.cdk-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.validate-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.validate-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102,126,234,0.4);
}

.validate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.validation-message {
  margin-top: 15px;
  padding: 10px;
  border-radius: 6px;
  font-weight: bold;
}

.validation-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.validation-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* 游戏主界面 */
.game-main {
  max-width: 1200px;
  margin: 0 auto;
}

/* 游戏信息栏 */
.game-info {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.info-item {
  background: rgba(255,255,255,0.95);
  padding: 15px 20px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  text-align: center;
  min-width: 150px;
}

.info-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.info-value {
  display: block;
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
}

.remaining-spins {
  color: #e74c3c;
  font-size: 1.4rem;
}

.session-id {
  font-family: monospace;
  font-size: 0.9rem;
  color: #666;
}

/* 转盘区域 */
.wheel-section {
  text-align: center;
  margin-bottom: 40px;
}

.wheel-container {
  position: relative;
  width: 400px;
  height: 400px;
  margin: 0 auto 20px;
}

/* 指针 */
.pointer {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
}

.pointer-triangle {
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-top: 25px solid #ff4444;
  position: relative;
}

.pointer-triangle::after {
  content: '';
  position: absolute;
  top: -23px;
  left: -10px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 20px solid #ffffff;
}

/* 转盘主体 */
.wheel {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  transition: transform 3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center center;
  box-shadow:
    0 0 0 8px rgba(255,255,255,0.8),
    0 0 0 12px rgba(0,0,0,0.1),
    0 8px 32px rgba(0,0,0,0.3);
  overflow: hidden;
}

.wheel.spinning {
  animation: wheelGlow 0.5s ease-in-out infinite alternate;
}

/* SVG转盘 */
.wheel-svg {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.wheel-segment-path {
  transition: all 0.3s ease;
  cursor: pointer;
}

.wheel-segment-path:hover {
  filter: brightness(1.1);
}

.segment-text-svg {
  pointer-events: none;
  user-select: none;
}

@keyframes wheelGlow {
  0% {
    box-shadow:
      0 0 0 8px rgba(255,255,255,0.8),
      0 0 0 12px rgba(0,0,0,0.1),
      0 8px 32px rgba(0,0,0,0.3);
  }
  100% {
    box-shadow:
      0 0 0 8px rgba(255,255,255,0.9),
      0 0 0 12px rgba(255,215,0,0.3),
      0 8px 32px rgba(0,0,0,0.4),
      0 0 20px rgba(255,215,0,0.2);
  }
}

/* 奖品扇形 */
.prize-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  transform-origin: 50% 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.prize-text {
  color: white;
  font-weight: bold;
  font-size: 14px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
  text-align: center;
  white-space: nowrap;
  position: absolute;
  transform-origin: center;
}

/* 中心按钮 */
.wheel-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 0 20px rgba(0,0,0,0.3),
    inset 0 2px 4px rgba(255,255,255,0.3);
  z-index: 100;
  border: 4px solid rgba(255,255,255,0.8);
  transition: all 0.2s ease;
  cursor: pointer;
}

.wheel-center:hover:not(.disabled) {
  transform: translate(-50%, -50%) scale(1.05);
  box-shadow:
    0 0 25px rgba(0,0,0,0.4),
    inset 0 2px 4px rgba(255,255,255,0.4);
}

.wheel-center:active:not(.disabled) {
  transform: translate(-50%, -50%) scale(0.95);
}

.wheel-center.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.center-content {
  text-align: center;
  color: white;
}

.center-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.center-text {
  font-size: 12px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* 转盘控制 */
.wheel-controls {
  margin-top: 20px;
}

.spin-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(255,107,107,0.3);
}

.spin-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255,107,107,0.4);
}

.spin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 历史记录 */
.history-section {
  background: rgba(255,255,255,0.95);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  max-width: 600px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
}

.no-history {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;
}

.history-item:hover {
  background-color: #f8f9fa;
}

.history-item.winning {
  background-color: #d4edda;
  border-left: 4px solid #28a745;
}

.history-time {
  font-size: 0.9rem;
  color: #666;
  min-width: 80px;
}

.history-result {
  flex: 1;
  text-align: right;
}

.prize-name {
  color: #28a745;
  font-weight: bold;
}

.no-prize {
  color: #6c757d;
}

/* 结果弹窗 */
.result-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.result-modal {
  background: white;
  border-radius: 20px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 20px 25px;
  position: relative;
}

.modal-title {
  margin: 0;
  font-size: 1.4rem;
  text-align: center;
}

.close-btn {
  position: absolute;
  top: 15px;
  right: 20px;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background-color: rgba(255,255,255,0.2);
}

.modal-body {
  padding: 30px 25px;
  text-align: center;
}

.winning-result .prize-icon,
.losing-result .consolation-icon {
  font-size: 4rem;
  margin-bottom: 15px;
}

.congratulations,
.consolation {
  margin: 0 0 15px 0;
  font-size: 1.5rem;
}

.congratulations {
  color: #28a745;
}

.consolation {
  color: #6c757d;
}

.prize-info {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  margin: 15px 0;
}

.prize-name {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.prize-description {
  color: #666;
  font-size: 1rem;
}

.encouragement {
  color: #666;
  margin: 10px 0;
}

.modal-footer {
  background: #f8f9fa;
  padding: 20px 25px;
  border-top: 1px solid #dee2e6;
  text-align: center;
}

.remaining-info {
  margin-bottom: 15px;
  color: #666;
}

.remaining-count {
  font-weight: bold;
  color: #e74c3c;
  font-size: 1.1rem;
}

.continue-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.continue-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40,167,69,0.3);
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.loading-spinner {
  text-align: center;
  color: white;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255,255,255,0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.1rem;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .game-title {
    font-size: 2rem;
  }

  .game-info {
    gap: 15px;
  }

  .info-item {
    min-width: 120px;
    padding: 12px 15px;
  }

  .wheel-container {
    width: 320px;
    height: 320px;
  }

  .wheel-center {
    width: 60px;
    height: 60px;
  }

  .center-icon {
    font-size: 16px;
  }

  .center-text {
    font-size: 10px;
  }

  .prize-text {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .lucky-wheel-game {
    padding: 15px;
  }

  .game-title {
    font-size: 1.8rem;
  }

  .game-subtitle {
    font-size: 1rem;
  }

  .cdk-section {
    padding: 20px;
  }

  .wheel-container {
    width: 280px;
    height: 280px;
  }

  .result-modal {
    width: 95%;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 20px;
  }
}
</style>
